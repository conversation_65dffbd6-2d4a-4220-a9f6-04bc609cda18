package com.ruoyi.conference.domain.mapper

import com.ruoyi.conference.domain.ConfGallery
import com.ruoyi.conference.domain.ConfModule
import com.ruoyi.conference.domain.dto.ConfGalleryDto
import com.ruoyi.conference.domain.dto.ConfModuleDto
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface ConfGalleryConvertMapper {

    companion object {
        val INSTANCE: ConfGalleryConvertMapper = Mappers.getMapper(ConfGalleryConvertMapper::class.java)
    }

    fun convert(source: ConfGalleryDto): ConfGallery
}