package com.ruoyi.conference.domain.dto

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableLogic
import com.baomidou.mybatisplus.annotation.TableName
import com.ruoyi.common.core.domain.BaseEntity
import com.ruoyi.conference.domain.BaseConfEntity
import jakarta.validation.constraints.NotNull
import java.util.Date

/**
 * 会务图文表
 */
data class ConfGalleryImgDto(

    /** 主键 */
    var id: Long? = null,

    /** 目录id */
    @field:NotNull(message = "目录id不能为空")
    var galleryId: Long? = null,

    /** 图片分类 */
    var imgCat: String? = null,

    /** 图片路径 */
    @field:NotNull(message = "图片路径不能为空")
    var imgUrl: String? = null,

)
