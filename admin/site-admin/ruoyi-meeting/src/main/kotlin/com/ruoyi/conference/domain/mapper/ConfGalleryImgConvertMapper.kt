package com.ruoyi.conference.domain.mapper

import com.ruoyi.conference.domain.ConfGallery
import com.ruoyi.conference.domain.ConfGalleryImg
import com.ruoyi.conference.domain.ConfModule
import com.ruoyi.conference.domain.dto.ConfGalleryDto
import com.ruoyi.conference.domain.dto.ConfGalleryImgDto
import com.ruoyi.conference.domain.dto.ConfModuleDto
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface ConfGalleryImgConvertMapper {

    companion object {
        val INSTANCE: ConfGalleryImgConvertMapper = Mappers.getMapper(ConfGalleryImgConvertMapper::class.java)
    }

    fun convert(source: ConfGalleryImgDto): ConfGalleryImg

}