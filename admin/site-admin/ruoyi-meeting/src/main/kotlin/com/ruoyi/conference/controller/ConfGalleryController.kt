package com.ruoyi.conference.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.utils.PageUtils.startPage
import com.ruoyi.conference.domain.ConfGallery
import com.ruoyi.conference.domain.ConfGalleryImg
import com.ruoyi.conference.domain.dto.ConfGalleryDto
import com.ruoyi.conference.domain.dto.ConfGalleryImgDto
import com.ruoyi.conference.domain.dto.ConfGalleryImgSaveBatchDto
import com.ruoyi.conference.service.ConfGalleryImgService
import com.ruoyi.conference.service.ConfGalleryService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 会务图文直播
 */
@RestController
@RequestMapping("/conference/gallery")
@Validated
open class ConfGalleryController(
    private val confGalleryService: ConfGalleryService,
    private val confGalleryImgService: ConfGalleryImgService
): BaseController() {

    /**
     * 查询会务图文相册列表
     */
    @RequestMapping("list")
    open fun listGallery(query: ConfGallery): AjaxResult {
        val list = confGalleryService.listGallery(query)
        return AjaxResult.success(list)
    }

    /**
     * 查询会务图文相册图片列表
     */
    @RequestMapping("img/page")
    open fun listGalleryImgPage(query: ConfGalleryImg): TableDataInfo {
        startPage()
        val list = confGalleryImgService.listGalleryImg(query)
        return getDataTable(list);
    }

    /**
     * 保存会务图文相册
     */
    @RequestMapping("save")
    open fun saveGallery(@Validated @RequestBody dto: ConfGalleryDto): AjaxResult {
        confGalleryService.saveGallery(dto)
        return AjaxResult.success()
    }

    /**
     * 保存会务图文相册图片
     */
    @RequestMapping("img/save")
    open fun saveGalleryImg(@Validated @RequestBody dto: ConfGalleryImgDto): AjaxResult {
        confGalleryImgService.saveGalleryImg(dto)
        return AjaxResult.success()
    }

    /**
     * 批量保存会务图文相册图片
     */
    @RequestMapping("img/saveBatch")
    open fun saveGalleryImgBatch(@Validated @RequestBody dto: ConfGalleryImgSaveBatchDto): AjaxResult {
        confGalleryImgService.saveGalleryImgBatch(dto)
        return AjaxResult.success()
    }

    /**
     * 删除会务信息
     */
    @DeleteMapping("/{id}")
    open fun del(@PathVariable id: Long): AjaxResult {
        confGalleryService.removeById(id)
        return AjaxResult.success()
    }

    /**
     * 删除会务图文相册图片
     */
    @DeleteMapping("/img/{ids}")
    open fun delImg(@PathVariable ids: List<Long>): AjaxResult {
        confGalleryImgService.delGalleryImg(ids)
        return AjaxResult.success()
    }

}