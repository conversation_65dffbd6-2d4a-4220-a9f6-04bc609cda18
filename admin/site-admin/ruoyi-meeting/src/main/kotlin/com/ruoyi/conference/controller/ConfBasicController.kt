package com.ruoyi.conference.controller

import com.github.pagehelper.PageInfo
import com.ruoyi.common.constant.HttpStatus
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.conference.domain.ConfBasic
import com.ruoyi.conference.domain.dto.ConfBasicDto
import com.ruoyi.conference.domain.mapper.ConfBasicConvertMapper
import com.ruoyi.conference.service.ConfAddrService
import com.ruoyi.conference.service.ConfBasicService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

/**
 * 会务主表 Controller
 */
@RestController
@RequestMapping("/conference/basic")
@Validated
open class ConfBasicController(
    private val confBasicService: ConfBasicService,
    private val confAddrService: ConfAddrService
) : BaseController() {


    /**
     * 查询会务分页列表
     */
    @GetMapping("page")
    open fun listPage(query: ConfBasic): TableDataInfo {
        startPage()
        val list = confBasicService.ktQuery()
            .like(!query.name.isNullOrEmpty(), ConfBasic::name, query.name)
            .eq(!query.status.isNullOrEmpty(), ConfBasic::status, query.status)
            .orderByDesc(ConfBasic::createTime)
            .list()
        val page:PageInfo<ConfBasic> = PageInfo(list)

        val addrsMap = confAddrService.getAddrsVoMap(list.map { it.id!! })
        val listVo = list.map {
            ConfBasicConvertMapper.INSTANCE.convertVo(it).apply { addrData = addrsMap[id!!] }
        }
        val rspData = TableDataInfo()
        rspData.code = HttpStatus.SUCCESS
        rspData.msg = "查询成功"
        rspData.rows = listVo
        rspData.total = page.total
        return rspData
    }

    /**
     * 获取会务信息
     */
    @GetMapping("/{id}")
    open fun get(@PathVariable id: Long): AjaxResult {
        val data = confBasicService.getConfBasic(id)
        return AjaxResult.success(data)
    }

    /**
     * 获取会务信息
     */
    @GetMapping("/code/{code}")
    open fun getByCode(@PathVariable code: String): AjaxResult {
        val data = confBasicService.getConfBasicByCode(code)
        return AjaxResult.success(data)
    }

    /**
     * 保存会务信息
     */
    @PostMapping("save")
    open fun save(@RequestBody dto: ConfBasicDto): AjaxResult {
        confBasicService.saveConfBasic(dto)
        return AjaxResult.success()
    }

    /**
     * 发布会务
     */
    @PostMapping("publish")
    open fun publish(@Validated @RequestBody dto: ConfBasicDto): AjaxResult {
        confBasicService.publishConfBasic(dto)
        return AjaxResult.success()
    }

    /**
     * 删除会务信息
     */
    @DeleteMapping("/{id}")
    open fun del(@PathVariable id: Long): AjaxResult {
        confBasicService.delConfBasic(id)
        return AjaxResult.success()
    }
}