package com.ruoyi.conference.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.conference.domain.ConfModule
import com.ruoyi.conference.domain.dto.ConfModuleDto
import com.ruoyi.conference.service.ConfModuleService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 会务ui模块 Controller
 */
@RestController
@RequestMapping("/conference/module")
@Validated
open class ConfModuleController(
    private val confModuleService: ConfModuleService
): BaseController() {


    /**
     * 查询ui模块列表
     */
    @GetMapping("list")
    open fun list(query: ConfModule): AjaxResult{
        val list = confModuleService.ktQuery()
            .like(!query.moduleType.isNullOrEmpty(), ConfModule::moduleType, query.moduleType)
            .orderByAsc(ConfModule::sort)
            .list()
        return AjaxResult.success(list)
    }

    /**
     * 获取ui模块详细信息
     */
    @GetMapping("/{id}")
    open fun get(@PathVariable id: Long): AjaxResult{
        val data = confModuleService.getById(id)
        return AjaxResult.success(data)
    }

    /**
     * 新增或修改ui模块
     */
    @PostMapping("save")
    open fun save(@RequestBody @Validated dto: ConfModuleDto): AjaxResult{
        confModuleService.saveModule(dto)
        return AjaxResult.success()
    }

    /**
     * 删除ui模块
     */
    @DeleteMapping("/{id}")
    open fun del(@PathVariable id: Long): AjaxResult{
        confModuleService.removeById(id)
        return AjaxResult.success()
    }
}