package com.ruoyi.conference.service

import cn.hutool.core.util.IdUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.github.yulichang.base.MPJBaseServiceImpl
import com.ruoyi.common.exception.ServiceException
import com.ruoyi.conference.domain.BaseConfEntity
import com.ruoyi.conference.domain.ConfBasic
import com.ruoyi.conference.domain.dto.ConfBasicDto
import com.ruoyi.conference.domain.enums.ConfStatus
import com.ruoyi.conference.domain.mapper.ConfBasicConvertMapper
import com.ruoyi.conference.domain.vo.ConfBasicVo
import com.ruoyi.conference.mapper.ConfBasicMapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 会务主表 ServiceImpl
 */
@Service
@Transactional(rollbackFor = [Throwable::class])
open class ConfBasicService(
    val objectMapper: ObjectMapper = jacksonObjectMapper(),
    val confAddrService: ConfAddrService,
    val confStaffGroupService: ConfStaffGroupService
) : MPJBaseServiceImpl<ConfBasicMapper, ConfBasic>(){


    /**
     * 获取会务信息
     */
    open fun getConfBasic(id: Long): ConfBasicVo? {
        val confBasic = this.getById(id) ?: return null
        val vo = ConfBasicConvertMapper.INSTANCE.convertVo(confBasic)
        vo.presetData = confBasic.presetJson?.takeIf { it.isNotBlank() }?.let { objectMapper.readValue(it) } ?: emptyList()
        vo.addrData = confAddrService.getAddrsVoByConfId(id)
        vo.groupData = confStaffGroupService.getGroupsVoByConfId(id)
        return vo
    }

    /**
     * 获取会务信息
     */
    open fun getConfBasicByCode(code: String): ConfBasicVo? {
        val confBasic = this.ktQuery().eq(ConfBasic::code, code)
            .last("limit 1").one() ?: return null
        val vo = ConfBasicConvertMapper.INSTANCE.convertVo(confBasic)
        vo.presetData = confBasic.presetJson?.takeIf { it.isNotBlank() }?.let { objectMapper.readValue(it) } ?: emptyList()
        vo.addrData = confAddrService.getAddrsVoByConfId(confBasic.id!!)
        return vo
    }

    /**
     * 保存会务信息
     */
    open fun saveConfBasic(dto: ConfBasicDto): ConfBasicVo {
        val isNew = dto.id == null
        val confBasic = ConfBasicConvertMapper.INSTANCE.convert(dto)
        confBasic.presetJson = dto.presetData
            ?.takeIf { it.isNotEmpty() }
            ?.let { objectMapper.writeValueAsString(it) }
            ?: ""
        if(isNew){
            confBasic.code = IdUtil.fastSimpleUUID()
            confBasic.status = ConfStatus.DRAFT.code
        } else {
            val db = getById(dto.id) ?: throw ServiceException("会务不存在")
            if(db.status != ConfStatus.DRAFT.code){
                throw ServiceException("会务已发布，无法保存")
            }
        }
        BaseConfEntity.setProps(confBasic)
        this.saveOrUpdate(confBasic)

        // 保存会务地址信息
        confAddrService.saveAddrs(confBasic.id!!, dto.addrData ?: emptyList())

        // 保存会务组信息
        confStaffGroupService.saveStaffGroup(confBasic.id!!, dto.groupData ?: emptyList())

        return getConfBasic(confBasic.id!!)!!
    }

    open fun publishConfBasic(dto: ConfBasicDto){
        val vo = saveConfBasic(dto)
        checkBeforeSubmit(vo)
        this.ktUpdate()
            .set(ConfBasic::status, ConfStatus.PUBLISHED.code)
            .eq(ConfBasic::id, vo.id)
            .update()
    }

    open fun delConfBasic(id: Long){
        val confBasic = getById(id) ?: throw ServiceException("会务不存在")
        if(confBasic.status != ConfStatus.DRAFT.code){
            throw ServiceException("会务已发布，无法删除")
        }
        this.removeById(id)
    }

    fun checkBeforeSubmit(vo: ConfBasicVo){
        if(vo.status != ConfStatus.DRAFT.code){
            throw ServiceException("会务已发布")
        }
        if(vo.presetData.isNullOrEmpty()){
            throw ServiceException("请添加首页模块")
        }
        if(vo.addrData.isNullOrEmpty()){
            throw ServiceException("请添加会务地址")
        }
    }


//    fun setProps(conf: ConfBasic) {
//        if (conf.id == null){
//            conf.createBy = SecurityUtils.getUsername()
//            conf.createTime = Date()
//        }
//        conf.updateBy = SecurityUtils.getUsername()
//        conf.updateTime = Date()
//    }

}
