package com.ruoyi.conference.service

import com.github.yulichang.base.MPJBaseServiceImpl
import com.ruoyi.conference.domain.BaseConfEntity
import com.ruoyi.conference.domain.ConfGallery
import com.ruoyi.conference.domain.dto.ConfGalleryDto
import com.ruoyi.conference.domain.mapper.ConfGalleryConvertMapper
import com.ruoyi.conference.mapper.ConfGalleryMapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 会务图文目录 ServiceImpl
 */
@Service
@Transactional(rollbackFor = [Throwable::class])
open class ConfGalleryService :
    MPJBaseServiceImpl<ConfGalleryMapper, ConfGallery>(){

    /**
     * 获取会务图文目录
     */
    open fun listGallery(query: ConfGallery): List<ConfGallery> {
        return this.ktQuery()
            .eq(ConfGallery::confId, query.confId)
            .orderByAsc(ConfGallery::sort)
            .list()
    }

    /**
     * 保存会务图文目录
     */
    open fun saveGallery(dto: ConfGalleryDto) {
        val entity = ConfGalleryConvertMapper.INSTANCE.convert(dto)
        BaseConfEntity.setProps(entity)
        this.saveOrUpdate(entity)
    }
}
