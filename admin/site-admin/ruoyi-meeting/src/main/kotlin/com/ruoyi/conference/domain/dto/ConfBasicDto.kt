package com.ruoyi.conference.domain.dto

import com.ruoyi.conference.domain.vo.ConfModuleData
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.util.Date

/**
 * 会务信息保存
 */
data class ConfBasicDto(

    /** 主键 */
    var id: Long? = null,

    /** 会务名称 */
    @field:NotBlank(message = "会务名称不能为空")
    var name: String? = null,

    /** 开始时间 */
    @field:NotNull(message = "开始时间不能为空")
    var startTime: Date? = null,

    /** 结束时间 */
    @field:NotNull(message = "结束时间不能为空")
    var endTime: Date? = null,

    /** 报名截止时间 */
    var signupDeadline: Date? = null,

    /** 首页模块JSON */
    var presetJson: String? = null,

    /** 是否使用留言板;0 否 1 是 */
    var useBoard: String? = null,

    /** banner图片路径 */
    var bannerUrl: String? = null,

    /** banner颜色 */
    var bannerColorClass: String? = null,

    /** banner标题 */
    var bannerTitle: String? = null,

    /** banner标题颜色 */
    var bannerTitleColorClass: String? = null,

    /** banner子标题 */
    var bannerSubtitle: String? = null,

    /** banner子标题颜色 */
    var bannerSubtitleColorClass: String? = null,

    /** 底图图片路径 */
    var backgroundUrl: String? = null,

    /** 底图颜色 */
    var backgroundColorClass: String? = null,

    /** 首页模块数据 */
    var presetData: List<ConfModuleData>? = null,

    /** 会务地址数据 */
    @field:Valid
    var addrData: List<ConfAddrDto>? = null,

    /** 会务组数据 */
    @field:Valid
    var groupData: List<ConfStaffGroupDto>? = null

)
