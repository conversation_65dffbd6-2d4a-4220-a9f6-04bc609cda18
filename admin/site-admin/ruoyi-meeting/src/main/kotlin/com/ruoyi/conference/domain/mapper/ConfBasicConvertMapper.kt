package com.ruoyi.conference.domain.mapper

import com.ruoyi.conference.domain.ConfBasic
import com.ruoyi.conference.domain.dto.ConfBasicDto
import com.ruoyi.conference.domain.vo.ConfBasicVo
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface ConfBasicConvertMapper {

    companion object {
        val INSTANCE: ConfBasicConvertMapper = Mappers.getMapper(ConfBasicConvertMapper::class.java)
    }

    fun convert(source: ConfBasicDto): ConfBasic

    fun convertVo(source: ConfBasic): ConfBasicVo

}