package com.ruoyi.conference.domain

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableLogic
import com.baomidou.mybatisplus.annotation.TableName
import com.ruoyi.common.core.domain.BaseEntity
import java.util.Date

/**
 * 会务图文表
 */
@TableName("CONF_GALLERY_IMG")
data class ConfGalleryImg(

    /** 目录id */
    var galleryId: Long? = null,

    /** 会务id */
    var confId: Long? = null,

    /** 图片分类 */
    var imgCat: String? = null,

    /** 图片路径 */
    var imgUrl: String? = null,

) : BaseConfEntity()
