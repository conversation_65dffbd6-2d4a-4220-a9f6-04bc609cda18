package com.ruoyi.conference.service

import com.github.yulichang.base.MPJBaseServiceImpl
import com.ruoyi.common.exception.ServiceException
import com.ruoyi.conference.domain.BaseConfEntity
import com.ruoyi.conference.domain.ConfGalleryImg
import com.ruoyi.conference.domain.dto.ConfGalleryImgDto
import com.ruoyi.conference.domain.dto.ConfGalleryImgSaveBatchDto
import com.ruoyi.conference.domain.mapper.ConfGalleryImgConvertMapper
import com.ruoyi.conference.mapper.ConfGalleryImgMapper
import com.ruoyi.conference.mapper.ConfGalleryMapper
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 会务图文 Service
 */
@Service
@Transactional(rollbackFor = [Throwable::class])
open class ConfGalleryImgService(
    private val confGalleryMapper: ConfGalleryMapper
) :
    MPJBaseServiceImpl<ConfGalleryImgMapper, ConfGalleryImg>() {

    /**
     * 获取会务图文图片
     */
    open fun listGalleryImg(query: ConfGalleryImg): List<ConfGalleryImg> {
        return this.ktQuery()
            .eq(ConfGalleryImg::galleryId, query.galleryId)
            .eq(!query.imgCat.isNullOrEmpty(), ConfGalleryImg::imgCat, query.imgCat)
            .orderByDesc(ConfGalleryImg::createTime)
            .list()
    }

    /**
     * 保存会务图文图片
     */
    open fun saveGalleryImg(dto: ConfGalleryImgDto) {
        val entity = ConfGalleryImgConvertMapper.INSTANCE.convert(dto)
        val confId = confGalleryMapper.selectById(dto.galleryId)?.confId ?: throw ServiceException("相册不存在")
        entity.confId = confId
        BaseConfEntity.setProps(entity)
        this.saveOrUpdate(entity)
    }


    /**
     * 批量保存会务图文图片
     */
    open fun saveGalleryImgBatch(dto: ConfGalleryImgSaveBatchDto) {
        val galleryImgs = mutableListOf<ConfGalleryImg>()
        val confId = confGalleryMapper.selectById(dto.galleryId)?.confId ?: throw ServiceException("相册不存在")
        dto.imgUrls?.forEach {
            val entity = ConfGalleryImg().apply {
                galleryId = dto.galleryId
                imgUrl = it
                imgCat = dto.imgCat
                this.confId = confId
            }
            BaseConfEntity.setProps(entity)
            galleryImgs.add(entity)
        }
        this.saveBatch(galleryImgs)
    }

    /**
     * 删除会务图文图片
     */
    open fun delGalleryImg(ids: List<Long>) {
        if(ids.isEmpty()){
            return
        }
        this.removeByIds(ids)
    }
}
