package com.ruoyi.conference.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.conference.domain.ConfModule
import com.ruoyi.conference.domain.ConfModulePreset
import com.ruoyi.conference.domain.dto.ConfModulePresetDto
import com.ruoyi.conference.service.ConfModulePresetService
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 会务ui模块预设 Controller
 */
@RestController
@RequestMapping("/conference/module/preset")
@Validated
open class ConfModulePresetController(
    private val confModulePresetService: ConfModulePresetService
) : BaseController()  {

    /**
     * 查询ui模块预设列表
     */
    @GetMapping("list")
    open fun list(query: ConfModulePreset): AjaxResult{
        val list = confModulePresetService.ktQuery()
            .like(!query.presetName.isNullOrEmpty(), ConfModulePreset::presetName, query.presetName)
            .list()
        return AjaxResult.success(list)
    }

    /**
     * 获取ui模块预设详细信息
     */
    @GetMapping("/{id}")
    open fun get(@PathVariable id: Long): AjaxResult{
        val data = confModulePresetService.getPreset(id)
        return AjaxResult.success(data)
    }

    /**
     * 保存预设
     */
    @PostMapping("save")
    open fun savePreset(@RequestBody @Validated dto: ConfModulePresetDto): AjaxResult{
        confModulePresetService.savePreset(dto)
        return AjaxResult.success()
    }

    /**
     * 删除预设
     */
    @DeleteMapping("/{id}")
    open fun del(@PathVariable id: Long): AjaxResult{
        confModulePresetService.removeById(id)
        return AjaxResult.success()
    }

}