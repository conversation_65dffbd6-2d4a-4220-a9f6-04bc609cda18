package com.ruoyi.conference.domain.mapper;

import com.ruoyi.conference.domain.ConfGalleryImg;
import com.ruoyi.conference.domain.dto.ConfGalleryImgDto;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T12:08:50+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.2 (Oracle Corporation)"
)
@Component
public class ConfGalleryImgConvertMapperImpl implements ConfGalleryImgConvertMapper {

    @Override
    public ConfGalleryImg convert(ConfGalleryImgDto source) {
        if ( source == null ) {
            return null;
        }

        ConfGalleryImg confGalleryImg = new ConfGalleryImg();

        confGalleryImg.setId( source.getId() );
        confGalleryImg.setGalleryId( source.getGalleryId() );
        confGalleryImg.setImgCat( source.getImgCat() );
        confGalleryImg.setImgUrl( source.getImgUrl() );

        return confGalleryImg;
    }
}
