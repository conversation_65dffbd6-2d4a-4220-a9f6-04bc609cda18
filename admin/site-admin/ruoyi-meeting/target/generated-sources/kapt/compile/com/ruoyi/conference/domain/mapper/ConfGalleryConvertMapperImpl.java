package com.ruoyi.conference.domain.mapper;

import com.ruoyi.conference.domain.ConfGallery;
import com.ruoyi.conference.domain.dto.ConfGalleryDto;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T12:08:50+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.2 (Oracle Corporation)"
)
@Component
public class ConfGalleryConvertMapperImpl implements ConfGalleryConvertMapper {

    @Override
    public ConfGallery convert(ConfGalleryDto source) {
        if ( source == null ) {
            return null;
        }

        ConfGallery confGallery = new ConfGallery();

        confGallery.setId( source.getId() );
        confGallery.setConfId( source.getConfId() );
        confGallery.setGalleryName( source.getGalleryName() );
        confGallery.setGalleryCover( source.getGalleryCover() );
        confGallery.setGalleryDate( source.getGalleryDate() );
        confGallery.setGalleryLocation( source.getGalleryLocation() );
        confGallery.setSort( source.getSort() );

        return confGallery;
    }
}
