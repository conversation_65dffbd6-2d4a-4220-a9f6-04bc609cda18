package com.ruoyi.common.utils.sms;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.sms.dto.SmsData;
import com.ruoyi.common.utils.sms.dto.SmsResult;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.Random;
import java.util.stream.Stream;

@Component
public class SmsUtils {

    @Value("${sms.sign}")
    private String sign;

    @Value("${sms.username}")
    private String username;

    @Value("${sms.password}")
    private String password;

    @Resource
    private SmsClient smsClient;

    /**
     * 发送短信
     * @param phoneNumber
     * @param msgContent
     * @return
     */
    public Boolean send(String phoneNumber, String msgContent) {
        SmsData smsData = new SmsData();
        smsData.setPhoneNumbers(Collections.singletonList(phoneNumber));
        smsData.setMsgContents(Stream.of(msgContent).map(
                t -> sign + t
        ).toList());
        String timestamp = String.valueOf(new Date().getTime());
        String jsonData = JSONObject.toJSONString(smsData);
        String md5 = Md5Utils.hash(
                username + password + jsonData + timestamp
        );
        SmsResult result = smsClient.send(username, md5, timestamp, jsonData);

        if (result != null) {
            return "MAS:000".equals(result.getCode());
        }
        return false;
    }

    // 生成指定长度的随机数字字符串
    public static String generateRandomCode(int length) {
        // 创建Random对象
        Random random = new Random();
        // 拼接随机数字
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            // 生成0到9之间的随机数字
            int digit = random.nextInt(10);
            code.append(digit);
        }
        return code.toString();
    }
}
