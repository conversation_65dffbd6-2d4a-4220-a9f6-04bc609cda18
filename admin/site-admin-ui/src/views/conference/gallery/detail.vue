<script setup lang="ts">
import { useRoute } from "vue-router";
import {onMounted, ref, watch, nextTick} from "vue";
import {getConfBasic} from "@/api/conference/basic";
import {listGallery, pageGalleryImg, saveGallery, saveGalleryImg, delGallery, delGalleryImg} from "@/api/conference/gallery";
import ImageUpload from "@/components/ImageUpload/index.vue";
import { ElMessage, ElMessageBox } from 'element-plus';

interface IConfGallery {
  /** 主键 */
  id?: number;
  /** 会务ID */
  confId?: number;
  /** 目录名称 */
  galleryName?: string;
  /** 封面 */
  galleryCover?: string;
  /** 日期 */
  galleryDate?: string;
  /** 地点 */
  galleryLocation?: string;
  /** 排序 */
  sort?: number;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}

interface IConfGalleryImg {
  /** 主键 */
  id?: number;
  /** 目录id */
  galleryId?: number;
  /** 会务id */
  confId?: number;
  /** 图片分类 */
  imgCat?: string;
  /** 图片路径 */
  imgUrl?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}

defineOptions({
  name: "GalleryDetail",
})
const route = useRoute();
// const confId = Number(route.params.confId);
const confBasic = ref<IConfBasic>()
const galleries = ref<IConfGallery[]>([])
const galleryImgs = ref<IConfGalleryImg[]>([])
const imgHost = import.meta.env.VITE_APP_BASE_API

const getGalleryList = async (confId: number) => {
  const {data} = await listGallery({ confId })
  galleries.value = data
}

const getConf = async (confId: number) => {
  const {data} = await getConfBasic(confId)
  confBasic.value = data
}

const searchForm = ref<IPageQuery<Partial<IConfGalleryImg>>>({
  galleryId: 0,
  imgCat: '',
  pageNum: 1,
  pageSize: 10,
})
const total = ref(0)

const getGalleryImgList = async () => {
  const data = await pageGalleryImg(searchForm.value)
  galleryImgs.value = data.rows
  total.value = data.total
}

watch(() => route.params.confId, (val) => {
  const confId = Number(val)
  getConf(confId)
  getGalleryList(confId)
  searchForm.value.galleryId = galleries.value[0]?.id || 0
  getGalleryImgList()
}, { immediate: true })
</script>

<template>
  <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
    <el-row>
      <el-col :span="4">

      </el-col>
      <el-col :span="20">
        <el-card shadow="never">
          <el-table :data="galleryImgs">
            <el-table-column label="图片" width="100">
              <template #default="scope">
                <el-image
                    style="width: 100px; height: 100px"
                    :src="imgHost + scope.row.imgUrl"
                    :preview-src-list="[imgHost + scope.row.imgUrl]"
                    :initial-index="0"
                    fit="cover"
                />
              </template>
            </el-table-column>
            <el-table-column label="图片分类" prop="imgCat" />
          </el-table>
          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="searchForm.pageNum"
              v-model:limit="searchForm.pageSize"
              @pagination="getGalleryImgList"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>

</style>