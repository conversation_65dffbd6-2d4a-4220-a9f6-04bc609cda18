<script setup lang="ts">
import {ref, onMounted} from 'vue'
import TailwindColorClassPicker from "@/components/TailwindColorClassPicker/index.vue";
import ImageUpload from "@/components/ImageUpload/index.vue";
import {ElMessage, ElMessageBox} from "element-plus";
import { UserFilled, Operation, Plus, Delete } from '@element-plus/icons-vue';
import ModuleDesigner from "@/views/conference/components/ModuleDesigner.vue";
import ConfModulePreviewDialog from "@/views/conference/components/ConfModulePreviewDialog.vue";
import {deleteConfBasic, getConfBasic, pageConfBasic, publishConfBasic, saveConfBasic} from "@/api/conference/basic";
import {listPreset, getPreset} from "@/api/conference/module";
import ConfLocMap from "@/views/conference/components/ConfLocMap.vue";
import ModulePreview from "@/views/conference/components/ModulePreview.vue";
import QRCode from 'qrcode'
import { parseTime } from '@/utils/ruoyi'


defineOptions({
  name: "ConfBasic",
})
const imgHost = import.meta.env.VITE_APP_BASE_API
const searchForm = ref<IPageQuery<Partial<IConfBasic>>>({
  pageNum: 1,
  pageSize: 10,
})
const loading = ref(false)
const dataList = ref<IConfBasic[]>([])
const total = ref(0);


// 待选模块
const dialogFormRef = ref()
const dialogLoading = ref(false)
const dialogTitle = ref('新增会务')
const dialogVisible = ref(false)
const dialogDisabled = ref(false)
const dialogForm = ref<IConfBasic>({presetData:[],addrData:[],timeRange:[],groupData:[], useBoard: '0'})
const rules = ref({
  name: [{ required: true, message: '请输入会务名称', trigger: 'blur' }],
  timeRange: [{ required: true, message: '请选择会务时间', trigger: 'change' }],
})

const activeTab = ref('first')

// 预览
const previewDialogVisible = ref(false)
const previewData = ref<IConfPreviewData>({
  presetData: []
})

const presetList = ref<IConfModulePreset[]>([])

const search = async () => {
  loading.value = true
  try {
    const data = await pageConfBasic(searchForm.value)
    total.value = data.total
    dataList.value = data.rows
  } finally {
    loading.value = false
  }
}
const reset = () => {
  searchForm.value = {
    pageNum: 1,
    pageSize: 10,
  }
  search()
}
const addConfBasic = async () => {
  dialogTitle.value = '新增会务'
  dialogVisible.value = true
  dialogDisabled.value = false
  await getPresets()
}

const editConfBasic = async (id:number) => {
  dialogTitle.value = '编辑会务'
  dialogLoading.value = true
  dialogVisible.value = true
  dialogDisabled.value = false
  try {
    await getPresets()
    const {data} = await getConfBasic(id)
    data.timeRange = [data.startTime, data.endTime]
    dialogForm.value = data
  } finally {
    dialogLoading.value = false
  }
}

const viewConfBasic = async (id:number) => {
  dialogTitle.value = '查看会务'
  dialogLoading.value = true
  dialogVisible.value = true
  dialogDisabled.value = true
  try {
    await getPresets()
    const {data} = await getConfBasic(id)
    data.timeRange = [data.startTime, data.endTime]
    dialogForm.value = data
  } finally {
    dialogLoading.value = false
  }
}

const saveInfo = (publish = false) => {
  dialogFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 发布时验证
      if (publish){
        if(!dialogForm.value.bannerColorClass && !dialogForm.value.bannerUrl){
          ElMessage.error('banner背景颜色和banner背景图至少设置一个')
          activeTab.value = 'second'
          return
        }
        if(!dialogForm.value.backgroundColorClass && !dialogForm.value.backgroundUrl){
          ElMessage.error('背景颜色和背景图至少设置一个')
          activeTab.value = 'second'
          return
        }
        if(!dialogForm.value.presetData?.length){
          ElMessage.error('请设置首页模块')
          activeTab.value = 'second'
          return
        }
        if(dialogForm.value.addrData?.length === 0){
          ElMessage.error('请设置会议地址')
          activeTab.value = 'third'
          return
        }

        // 验证会务组数据
        if (dialogForm.value.groupData && dialogForm.value.groupData.length > 0) {
          for (let i = 0; i < dialogForm.value.groupData.length; i++) {
            const group = dialogForm.value.groupData[i]
            if (!group.groupName || group.groupName.trim() === '') {
              ElMessage.error(`第${i + 1}个会务组的组名称不能为空`)
              activeTab.value = 'first'
              return
            }

            // 验证人员信息
            if (group.staffList && group.staffList.length > 0) {
              for (let j = 0; j < group.staffList.length; j++) {
                const staff = group.staffList[j]
                if (!staff.name || staff.name.trim() === '') {
                  ElMessage.error(`会务组"${group.groupName}"中第${j + 1}个人员的姓名不能为空`)
                  activeTab.value = 'first'
                  return
                }
                if (staff.mobile && !/^1[3-9]\d{9}$/.test(staff.mobile)) {
                  ElMessage.error(`会务组"${group.groupName}"中人员"${staff.name}"的手机号格式不正确`)
                  activeTab.value = 'first'
                  return
                }
              }
            }
          }
        }
      }

      try {
        dialogLoading.value = true
        const api = publish ? publishConfBasic : saveConfBasic
        dialogForm.value.startTime = dialogForm.value.timeRange?.[0]
        dialogForm.value.endTime = dialogForm.value.timeRange?.[1]
        await api(dialogForm.value)
        dialogVisible.value = false
        await search()
      } finally {
        dialogLoading.value = false
      }
    } else {
      activeTab.value = 'first'
    }
  })
}

const delConfBasic = async (id: number) => {
  try {
    await deleteConfBasic(id)
    await search()
  } catch (e) {
    console.log(e)
  }
}

const previewConfBasic = async (id: number) => {
  try {
    const {data} = await getConfBasic(id)
    previewData.value = data
    previewDialogVisible.value = true
  } catch (e) {
    console.log(e)
  }
}

const confIndexPrefix = import.meta.env.VITE_CONFERENCE_INDEX
const confSignPrefix = import.meta.env.VITE_CONFERENCE_SIGN
const confCodeUrl = ref("");
const confSignUrl = ref("");
const previewQRCode = async (code: string) => {
  try {
    confCodeUrl.value = await new QRCode.toDataURL(confIndexPrefix + '/' + code,{margin:1})
    confSignUrl.value = await new QRCode.toDataURL(confSignPrefix + '/' + code,{margin:1})
  } catch (e) {
    console.log(e)
  } finally {
    dialogLoading.value = false
  }
}

const getPresets = async () => {
  try {
    const {data} = await listPreset()
    presetList.value = data
  } catch (e) {
    console.log(e)
  }
}

const setFromPreset = (preset: IConfModulePreset) => {
  ElMessageBox.confirm(`是否使用预设"${preset.presetName}"覆盖当前设置？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    dialogForm.value.presetData = preset.presetJson ? JSON.parse(preset.presetJson) : []
    dialogForm.value.bannerTitle = preset.bannerTitle
    dialogForm.value.bannerSubtitle = preset.bannerSubtitle
    dialogForm.value.bannerTitleColorClass = preset.bannerTitleColorClass
    dialogForm.value.bannerSubtitleColorClass = preset.bannerSubtitleColorClass
    dialogForm.value.bannerUrl = preset.bannerUrl || ''
    dialogForm.value.bannerColorClass = preset.bannerColorClass
    dialogForm.value.backgroundUrl = preset.backgroundUrl || ''
    dialogForm.value.backgroundColorClass = preset.backgroundColorClass
  })
}

const onClosed = () => {
  dialogForm.value = {presetData:[],addrData:[],timeRange:[],groupData:[], useBoard: '0'}
  dialogFormRef.value.resetFields()
  activeTab.value = 'first'
}

// 会务组管理方法
const addStaffGroup = () => {
  if (!dialogForm.value.groupData) {
    dialogForm.value.groupData = []
  }
  const newGroup: IConfStaffGroup = {
    groupName: '',
    sort: dialogForm.value.groupData.length + 1,
    staffList: []
  }
  dialogForm.value.groupData.push(newGroup)
}

const removeStaffGroup = (groupIndex: number) => {
  ElMessageBox.confirm('确定要删除这个会务组吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    dialogForm.value.groupData?.splice(groupIndex, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const addStaffToGroup = (groupIndex: number) => {
  const group = dialogForm.value.groupData?.[groupIndex]
  if (group) {
    if (!group.staffList) {
      group.staffList = []
    }
    const newStaff: IConfStaffData = {
      name: '',
      mobile: ''
    }
    group.staffList.push(newStaff)
  }
}

const removeStaffFromGroup = (groupIndex: number, staffIndex: number) => {
  const group = dialogForm.value.groupData?.[groupIndex]
  if (group && group.staffList) {
    group.staffList.splice(staffIndex, 1)
  }
}

const validateGroupName = (group: IConfStaffGroup, groupIndex: number) => {
  if (!group.groupName || group.groupName.trim() === '') {
    ElMessage.warning('请输入组名称')
    return false
  }

  // 检查组名称是否重复
  const duplicateIndex = dialogForm.value.groupData?.findIndex((g, index) =>
    index !== groupIndex && g.groupName === group.groupName
  )

  if (duplicateIndex !== undefined && duplicateIndex >= 0) {
    ElMessage.warning('组名称不能重复')
    return false
  }

  return true
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="p-2 flex flex-col gap-2 h-full bg-[#f0f2f5]">
    <div class="p-2 bg-white border rounded">
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="会务名称" prop="name">
          <el-input v-model="searchForm.name" placeholder="请输入" clearable></el-input>
        </el-form-item>

        <el-form-item label-width="50px">
          <el-button type="primary" icon="Search" @click="search">查询</el-button>
          <el-button icon="RefreshRight" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="bg-white border rounded h-full" v-loading="loading">
      <div class="flex gap-2 p-2">
        <el-button type="primary" icon="plus" plain @click="addConfBasic">添加</el-button>
      </div>
      <div ref="table" class=" w-full">
        <el-table :data="dataList" row-key="id" border class="text-xs w-full" stripe>
          <el-table-column prop="name" label="会务名称" align="center"/>
          <el-table-column prop="startTime" label="会务时间" align="center" width="280px">
            <template #default="{row}">
              {{ `${parseTime(row.startTime)} ~ ${parseTime(row.endTime)}` }}
            </template>
          </el-table-column>
          <el-table-column label="会务地点" align="center">
            <template #default="{row}">
              <div v-for="item in row.addrData" :key="item.id">
                {{ item.confAddr }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="首页预览" align="center">
            <template #default="{row}">
              <el-button type="primary" text size="small" @click="previewConfBasic(row.id)">预览</el-button>
            </template>
          </el-table-column>
          <el-table-column label="会务访问/签到二维码" align="center">
            <template #default="{row}">
              <el-popover
                  placement="top"
                  trigger="click"
                  width="250"
                  content="this is content, this is content, this is content"
              >
                <template #reference>
                  <el-button type="primary" text size="small" @click="previewQRCode(row.code)">预览</el-button>
                </template>
                <div class="text-center font-bold text-lg">{{row.name}}</div>
                <div class="flex gap-2 justify-center">
                  <div>
                    <el-image style="width: 100px; height: 100px" :src="confCodeUrl" fit="fill" />
                    <div class="text-center font-bold">会务访问</div>
                  </div>
                  <div>
                    <el-image style="width: 100px; height: 100px" :src="confSignUrl" fit="fill" />
                    <div class="text-center font-bold">会务签到</div>
                  </div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="statusName" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="260px">
            <template #default="{row}">
              <el-button type="primary" text size="small" @click="viewConfBasic(row.id)">查看</el-button>
              <el-button v-if="row.status === '0'" type="primary" text size="small" @click="editConfBasic(row.id)">编辑</el-button>
              <el-popconfirm title="是否删除？" @confirm="delConfBasic(row.id)">
                <template #reference>
                  <el-button v-if="row.status === '0'" type="danger" text size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="searchForm.pageNum"
            v-model:limit="searchForm.pageSize"
            @pagination="search"
        />
      </div>
    </div>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="960px" @closed="onClosed" fullscreen>
      <div v-loading="dialogLoading">
        <div class="flex mb-4 pl-4" v-if="!dialogDisabled">
          <el-button type="primary" @click="saveInfo()" :loading="dialogLoading">保 存</el-button>
          <el-button type="primary" @click="saveInfo(true)" :loading="dialogLoading">保存并发布</el-button>
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </div>
        <el-form ref="dialogFormRef" :model="dialogForm" label-width="150px" :rules="rules" :disabled="dialogDisabled" :hide-required-asterisk="dialogDisabled">
          <el-tabs v-model="activeTab" tab-position="left">
            <el-tab-pane label="基本信息" name="first">
              <el-row :gutter="20">
               <el-col :span="12">
                 <div class="mb-6 pb-3 border-b-2 border-blue-100 bg-gradient-to-r from-blue-50 to-transparent rounded-t-lg px-4 py-3 -mx-2">
                   <div class="flex items-center space-x-2">
                     <div class="w-1 h-5 bg-blue-500 rounded-full"></div>
                     <h3 class="text-lg font-semibold text-gray-800">会务信息</h3>
                   </div>
                 </div>

                 <template v-if="!dialogDisabled">
                   <el-form-item label="会务名称" prop="name">
                     <el-input
                         v-model.trim="dialogForm.name"
                         placeholder="请输入会务名称"
                         clearable
                     />
                   </el-form-item>
                   <el-form-item label="会务时间" prop="timeRange">
                     <el-date-picker
                         v-model="dialogForm.timeRange"
                         type="datetimerange"
                         range-separator="至"
                         start-placeholder="开始时间"
                         end-placeholder="结束时间"
                     />
                   </el-form-item>
                   <el-form-item label="报名截止时间" prop="signupDeadline">
                     <el-date-picker
                         v-model="dialogForm.signupDeadline"
                         type="datetime"
                         placeholder="请选择报名截止时间"/>
                   </el-form-item>
                   <el-form-item label="是否启用留言板" prop="useBoard">
                     <el-switch v-model="dialogForm.useBoard" active-text="启用" inactive-text="禁用" inactive-value="0" active-value="1" />
                   </el-form-item>
                 </template>

                 <template v-else>
                   <div class="space-y-4">
                     <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                       <div class="flex items-center justify-between mb-2">
                         <span class="text-sm font-medium text-gray-600">会务名称</span>
                       </div>
                       <div class="text-base text-gray-800">
                         {{ dialogForm.name || '未填写' }}
                       </div>
                     </div>

                     <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                       <div class="flex items-center justify-between mb-2">
                         <span class="text-sm font-medium text-gray-600">会务时间</span>
                       </div>
                       <div class="text-base text-gray-800">
                         <template v-if="dialogForm.startTime && dialogForm.endTime">
                           {{ parseTime(dialogForm.startTime) }} ~ {{ parseTime(dialogForm.endTime) }}
                         </template>
                         <template v-else-if="dialogForm.timeRange && dialogForm.timeRange.length === 2">
                           {{ parseTime(dialogForm.timeRange[0]) }} ~ {{ parseTime(dialogForm.timeRange[1]) }}
                         </template>
                         <span v-else class="text-gray-400">未设置</span>
                       </div>
                     </div>

                     <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                       <div class="flex items-center justify-between mb-2">
                         <span class="text-sm font-medium text-gray-600">报名截止时间</span>
                       </div>
                       <div class="text-base text-gray-800">
                         <template v-if="dialogForm.signupDeadline">
                           {{ parseTime(dialogForm.signupDeadline) }}
                         </template>
                         <span v-else class="text-gray-400">未设置</span>
                       </div>
                     </div>
                   </div>
                 </template>
               </el-col>
                <el-col :span="12">
                  <div class="mb-6 pb-3 border-b-2 border-green-100 bg-gradient-to-r from-green-50 to-transparent rounded-t-lg px-4 py-3 -mx-2 flex justify-between items-center">
                    <div class="flex items-center space-x-2">
                      <div class="w-1 h-5 bg-green-500 rounded-full"></div>
                      <h3 class="text-lg font-semibold text-gray-800">会务组信息</h3>
                    </div>
                    <el-button
                      v-if="!dialogDisabled"
                      type="primary"
                      @click="addStaffGroup">
                      添加会务组
                    </el-button>
                  </div>

                  <!-- 会务组列表 -->
                  <div class="space-y-3 max-h-80 overflow-y-auto">
                    <!-- 编辑模式 -->
                    <template v-if="!dialogDisabled">
                      <div
                        v-for="(group, groupIndex) in dialogForm.groupData"
                        :key="group.id || groupIndex"
                        class="bg-white border border-gray-200 rounded-lg shadow-sm">

                        <!-- 会务组头部 -->
                        <div class="flex items-center justify-between mb-3">
                          <div class="flex items-center space-x-3 flex-1">
                            <el-input
                              v-model="group.groupName"
                              placeholder="请输入组名称"
                              class="flex-1"
                              @blur="validateGroupName(group, groupIndex)"
                            />
                            <el-input-number
                              v-model="group.sort"
                              placeholder="排序"
                              :min="1"
                              :max="999"
                              controls-position="right"
                              class="w-24"
                            />
                          </div>
                          <div class="flex items-center space-x-2 ml-3">
                            <el-button
                              type="primary"
                              @click="addStaffToGroup(groupIndex)">
                              添加人员
                            </el-button>
                            <el-button
                              type="danger"
                              @click="removeStaffGroup(groupIndex)">
                              删除组
                            </el-button>
                          </div>
                        </div>

                        <!-- 人员列表 -->
                        <div v-if="group.staffList && group.staffList.length > 0" class="space-y-2">
                          <div
                            v-for="(staff, staffIndex) in group.staffList"
                            :key="staffIndex"
                            class="flex items-center space-x-3 bg-gray-50 p-3 rounded-md">
                            <el-input
                              v-model="staff.name"
                              placeholder="请输入姓名"
                              class="flex-1"
                            />
                            <el-input
                              v-model="staff.mobile"
                              placeholder="请输入手机号"
                              class="flex-1"
                              maxlength="11"
                              show-word-limit
                            />
                            <el-button
                              type="danger"
                              @click="removeStaffFromGroup(groupIndex, staffIndex)">
                              删除
                            </el-button>
                          </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-else class="text-center text-gray-400 py-6">
                          <el-icon class="text-xl mb-1"><UserFilled /></el-icon>
                          <div class="text-sm">暂无人员，点击"添加人员"按钮添加</div>
                        </div>
                      </div>
                    </template>

                    <!-- 查看模式 -->
                    <template v-else>
                      <div
                        v-for="(group, groupIndex) in dialogForm.groupData"
                        :key="group.id || groupIndex"
                        class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">

                        <!-- 会务组信息展示 -->
                        <div class="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                          <div class="flex items-center space-x-4">
                            <div class="text-base font-medium text-gray-800">
                              {{ group.groupName || '未命名组' }}
                            </div>
                            <div class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              排序: {{ group.sort || '-' }}
                            </div>
                          </div>
                        </div>

                        <!-- 人员信息展示 -->
                        <div v-if="group.staffList && group.staffList.length > 0" class="space-y-2">
                          <div class="text-sm font-medium text-gray-600 mb-2">人员列表：</div>
                          <div
                            v-for="(staff, staffIndex) in group.staffList"
                            :key="staffIndex"
                            class="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                            <div class="flex items-center space-x-4">
                              <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-500">姓名:</span>
                                <span class="text-sm font-medium text-gray-800">
                                  {{ staff.name || '未填写' }}
                                </span>
                              </div>
                              <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-500">手机:</span>
                                <span class="text-sm text-gray-800">
                                  {{ staff.mobile || '未填写' }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 无人员状态 -->
                        <div v-else class="text-center text-gray-400 py-4 bg-gray-50 rounded-md">
                          <el-icon class="text-lg mb-1"><UserFilled /></el-icon>
                          <div class="text-sm">暂无人员信息</div>
                        </div>
                      </div>
                    </template>
                  </div>

                  <!-- 空状态 -->
                  <div v-if="!dialogForm.groupData || dialogForm.groupData.length === 0"
                       class="text-center text-gray-400 py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                    <el-icon class="text-2xl mb-2"><Operation /></el-icon>
                    <div class="text-sm">
                      {{ dialogDisabled ? '暂无会务组信息' : '暂无会务组，点击"添加会务组"按钮开始创建' }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="second">
              <template #label>
                <el-badge v-if="dialogForm.presetData?.length === 0" is-dot >
                  <div>首页设计</div>
                </el-badge>
                <div v-else>首页设计</div>
              </template>
              <el-row :gutter="10" v-if="!dialogDisabled">
                <el-col v-if="presetList.length > 0">
                  <div class="mb-4">
                    <span class="text-sm font-bold inline-block w-[150px] text-right pr-[12px]">首页预设快捷选择</span>
                    <el-button v-for="item in presetList" :key="item.id" class="m-1" @click="setFromPreset(item)">
                      {{ item.presetName }}
                    </el-button>
                  </div>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="banner标题" prop="bannerTitle">
                    <el-input
                        v-model="dialogForm.bannerTitle"
                        placeholder="请输入banner标题"
                        clearable
                    />
                  </el-form-item>
                  <el-form-item label="banner子标题" prop="bannerSubtitle">
                    <el-input
                        v-model="dialogForm.bannerSubtitle"
                        placeholder="请输入banner子标题"
                        clearable
                    />
                  </el-form-item>
                  <el-form-item label="banner标题颜色" prop="bannerTitleColorClass">
                    <tailwind-color-class-picker
                        v-model="dialogForm.bannerTitleColorClass"
                        type="text"
                        :disabled="dialogDisabled"
                    />
                  </el-form-item>
                  <el-form-item label="banner子标题颜色" prop="bannerSubtitleColorClass">
                    <tailwind-color-class-picker
                        v-model="dialogForm.bannerSubtitleColorClass"
                        type="text"
                        :disabled="dialogDisabled"
                    />
                  </el-form-item>
                  <el-form-item label="banner背景图" prop="bannerUrl">
                    <ImageUpload
                        v-model="dialogForm.bannerUrl"
                        :limit="1"
                        :fileSize="5"
                        :fileType="['png', 'jpg', 'jpeg']"
                        :isShowTip="true"
                    />
                  </el-form-item>
                  <el-form-item label="banner背景颜色" prop="bannerColorClass">
                    <tailwind-color-class-picker
                        v-model="dialogForm.bannerColorClass"
                        type="background"
                        :disabled="dialogDisabled"
                    />
                  </el-form-item>
                  <el-form-item label="背景图" prop="backgroundUrl">
                    <ImageUpload
                        v-model="dialogForm.backgroundUrl"
                        :limit="1"
                        :fileSize="5"
                        :fileType="['png', 'jpg', 'jpeg']"
                        :isShowTip="true"
                    />
                  </el-form-item>
                  <el-form-item label="背景颜色" prop="backgroundColorClass">
                    <tailwind-color-class-picker
                        v-model="dialogForm.backgroundColorClass"
                        type="background"
                        :disabled="dialogDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <module-designer v-model="dialogForm.presetData"/>
                </el-col>
              </el-row>
              <el-row :gutter="10" v-else>
                <el-col :span="24">
                  <module-preview :preview-data="dialogForm"/>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane name="third" :lazy="true">
              <template #label>
                <el-badge v-if="dialogForm.addrData?.length === 0" is-dot >
                  <div>会议地址</div>
                </el-badge>
                <div v-else>会议地址</div>
              </template>
              <el-row :gutter="10">
                <el-col>
                  <conf-loc-map v-model="dialogForm.addrData" :disabled="dialogDisabled"/>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
    </el-dialog>

    <conf-module-preview-dialog v-model="previewDialogVisible" :preview-data="previewData"/>
  </div>
</template>

<style scoped>
.icon-input-wrapper {
  width: 100%;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 32px;
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

.preview-icon {
  font-size: 16px;
  color: #409eff;
}

.no-icon-text {
  font-size: 12px;
  color: #c0c4cc;
}

.icon-preview-large {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.large-preview-icon {
  font-size: 24px;
  color: #409eff;
  min-width: 24px;
}

.icon-class-text {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
}
</style>