// @ts-ignore
import request from '@/utils/request.js'

/**
 * 获取会务图文相册
 * @param query
 */
export function listGallery(query: IConfGallery): Promise<IResult<IConfGallery[]>> {
  return request({
    url: '/conference/gallery/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取会务图文相册图片
 * @param query
 */
export function pageGalleryImg(query: IPageQuery<Partial<IConfGalleryImg>>): Promise<IPage<IConfGalleryImg>> {
  return request({
    url: '/conference/gallery/img/page',
    method: 'get',
    params: query
  })
}

/**
 * 保存会务图文相册
 * @param data
 */
export function saveGallery(data: IConfGallery) {
  return request({
    url: '/conference/gallery',
    method: 'post',
    data
  })
}

/**
 * 保存会务图文相册图片
 * @param data
 */
export function saveGalleryImg(data: IConfGalleryImg) {
  return request({
    url: '/conference/gallery/img',
    method: 'post',
    data
  })
}

/**
 * 删除会务图文相册
 * @param id
 */
export function delGallery(id: number) {
  return request({
    url: `/conference/gallery/${id}`,
    method: 'delete'
  })
}

/**
 * 删除会务图文相册图片
 * @param ids
 */
export function delGalleryImg(ids: number[]) {
  return request({
    url: `/conference/gallery/img/${ids.join(',')}`,
    method: 'delete'
  })
}