// @ts-ignore
import request from '@/utils/request.js'

// 会议模块
export function listModule(query?: Partial<IConfModule>): Promise<IResult<IConfModule[]>> {
  return request({
    url: '/conference/module/list',
    method: 'get',
    params: query
  })
}

export function getModule(id: number): Promise<IResult<IConfModule>> {
  return request({
    url: '/conference/module/' + id,
    method: 'get'
  })
}

export function saveModule(data: Partial<IConfModule>) {
  return request({
    url: '/conference/module/save',
    method: 'post',
    data: data
  })
}

export function deleteModule(id: number): Promise<IResult<any>>{
  return request({
    url: '/conference/module/' + id,
    method: 'delete'
  })
}

// 会议模块预设
export function listPreset(query?: Partial<IConfModulePreset>): Promise<IResult<IConfModulePreset[]>> {
  return request({
    url: '/conference/module/preset/list',
    method: 'get',
    params: query
  })
}

export function getPreset(id: number): Promise<IResult<IConfModulePreset>> {
  return request({
    url: '/conference/module/preset/' + id,
    method: 'get'
  })
}

export function savePreset(data: Partial<IConfModulePreset>) {
  return request({
    url: '/conference/module/preset/save',
    method: 'post',
    data: data
  })
}

export function deletePreset(id: number): Promise<IResult<any>>{
  return request({
    url: '/conference/module/preset/' + id,
    method: 'delete'
  })
}