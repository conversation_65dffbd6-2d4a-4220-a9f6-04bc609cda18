// @ts-ignore
import request from '@/utils/request.js'

// 会议分页列表
export function pageConfBasic(query?: IPageQuery<Partial<IConfBasic>>): Promise<IPage<IConfBasic>> {
  return request({
    url: '/conference/basic/page',
    method: 'get',
    params: query
  })
}

// 会议详情
export function getConfBasic(id: number): Promise<IResult<IConfBasic>> {
  return request({
    url: `/conference/basic/${id}`,
    method: 'get'
  })
}

// 保存会议
export function saveConfBasic(data: Partial<IConfBasic>): Promise<void> {
  return request({
    url: '/conference/basic/save',
    method: 'post',
    data
  })
}

// 发布会议
export function publishConfBasic(data: Partial<IConfBasic>): Promise<void> {
  return request({
    url: '/conference/basic/publish',
    method: 'post',
    data
  })
}

// 删除会议
export function deleteConfBasic(id: number): Promise<void> {
  return request({
    url: `/conference/basic/${id}`,
    method: 'delete'
  })
}