interface IConfGallery {
  /** 主键 */
  id?: number;
  /** 会务ID */
  confId?: number;
  /** 目录名称 */
  galleryName?: string;
  /** 封面 */
  galleryCover?: string;
  /** 日期 */
  galleryDate?: string;
  /** 地点 */
  galleryLocation?: string;
  /** 排序 */
  sort?: number;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}

interface IConfGalleryImg {
  /** 主键 */
  id?: number;
  /** 目录id */
  galleryId?: number;
  /** 会务id */
  confId?: number;
  /** 图片分类 */
  imgCat?: string;
  /** 图片路径 */
  imgUrl?: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateTime?: Date;
}