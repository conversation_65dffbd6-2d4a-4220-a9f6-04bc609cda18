09:35:20.920 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
09:35:20.959 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 5272 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
09:35:20.960 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
09:35:23.432 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
09:35:24.525 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
09:35:24.527 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:24.527 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
09:35:24.602 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:25.508 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
09:35:26.052 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
09:35:27.832 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
09:35:30.117 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:35:30.131 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:35:30.131 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:35:30.132 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:35:30.132 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:35:30.133 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:35:30.133 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:35:30.133 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@71b7029f
09:35:34.587 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
09:35:36.538 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
09:35:36.772 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
09:35:36.772 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:35:36.773 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:35:37.190 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:35:37.333 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
09:35:37.335 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:35:37.335 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
09:35:37.335 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
09:35:37.335 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
09:35:37.568 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
09:35:37.968 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: 482c6adc-91d4-11f0-9ba5-2a8d686d9ea7, key: news_release_audit_process, name: news_release_audit_process }
09:35:43.753 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
09:35:43.765 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:35:43.939 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:35:43.939 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:35:44.118 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:35:44.118 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:36:32.633 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:36:32.634 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:36:32.753 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:36:32.754 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:36:32.775 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 72.234 seconds (process running for 73.606)
09:36:33.282 [RMI TCP Connection(4)-192.168.30.33] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:39:15.089 [tomcat-handler-1] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
09:39:15.089 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
09:39:20.126 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
13:59:46.806 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
13:59:46.856 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 18069 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
13:59:46.857 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
13:59:49.470 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:59:50.605 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
13:59:50.607 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:50.607 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
13:59:50.662 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:51.595 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:59:52.228 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
13:59:54.049 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
13:59:56.262 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:59:56.274 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:59:56.274 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:59:56.275 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:59:56.276 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:59:56.276 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:59:56.276 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:59:56.276 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@8e09d2d
14:00:00.768 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
14:00:02.463 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
14:00:02.634 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
14:00:02.635 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:00:02.635 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:00:03.044 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
14:00:03.099 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
14:00:03.101 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:00:03.102 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
14:00:03.102 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
14:00:03.102 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
14:00:03.162 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
14:00:08.041 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
14:00:08.053 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:00:08.256 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:00:08.257 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:00:08.326 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:00:08.327 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:00:20.910 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:00:20.910 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
14:00:20.939 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
14:00:20.939 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:00:20.957 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 34.605 seconds (process running for 35.993)
14:00:21.561 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:00:41.295 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
14:00:47.205 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
15:51:48.795 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1882] - {dataSource-1} failContinuous is true
15:53:29.429 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1887] - {dataSource-1} failContinuous is false
16:13:56.566 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1882] - {dataSource-1} failContinuous is true
16:16:39.562 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1887] - {dataSource-1} failContinuous is false
16:19:40.620 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1882] - {dataSource-1} failContinuous is true
16:19:45.830 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1887] - {dataSource-1} failContinuous is false
16:30:17.860 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1882] - {dataSource-1} failContinuous is true
16:31:13.078 [Druid-ConnectionPool-Create-1907110299] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1887] - {dataSource-1} failContinuous is false
17:03:26.409 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:03:26.412 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:03:26.412 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:03:26.412 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:03:26.413 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:03:26.413 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:03:26.413 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:03:26.413 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:03:26.418 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:03:26.566 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
17:03:26.567 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
17:03:26.567 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
17:03:26.567 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
17:03:26.575 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:03:26.575 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:03:26.575 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:03:26.577 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:03:26.600 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
17:03:26.617 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
17:03:39.309 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
17:03:39.363 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 26472 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
17:03:39.364 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
17:03:42.152 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:03:43.306 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
17:03:43.307 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:03:43.308 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
17:03:43.359 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
17:03:44.300 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:03:44.910 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:03:46.847 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
17:03:49.186 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:03:49.199 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:03:49.199 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:03:49.200 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:03:49.200 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:03:49.201 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:03:49.201 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:03:49.209 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7bf2630c
17:03:53.826 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
17:03:55.606 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
17:03:55.786 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
17:03:55.787 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:03:55.787 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:03:56.204 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:03:56.355 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
17:03:56.357 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
17:03:56.358 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
17:03:56.358 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
17:03:56.358 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
17:03:56.513 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,90] - An existing deployment of version 1 matching the current one was found, no need to deploy again.
17:04:01.778 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
17:04:01.791 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:04:02.167 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:04:02.168 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:04:02.263 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:04:02.263 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:04:31.836 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:04:31.837 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:04:31.998 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:04:31.999 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:04:32.017 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 53.207 seconds (process running for 54.697)
17:04:33.047 [RMI TCP Connection(3)-192.168.30.33] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:04:48.657 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
17:20:27.672 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:20:27.674 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:20:27.675 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:20:27.782 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,212] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
17:20:27.782 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,119] - {} stopped async job due acquisition
17:20:27.782 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,103] - {} stopped resetting expired jobs
17:20:27.782 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,119] - {} stopped async job due acquisition
17:20:27.790 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:20:27.791 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:20:27.791 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:20:27.792 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:20:27.829 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
17:20:27.841 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
